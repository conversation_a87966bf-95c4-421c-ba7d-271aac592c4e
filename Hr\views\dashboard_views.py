# =============================================================================
# ElDawliya HR Management System - Dashboard Views
# =============================================================================
# Comprehensive HR dashboard with statistics and KPIs
# Supports RTL Arabic interface and modern Django patterns
# =============================================================================

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
from django.views.generic import TemplateView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count, Sum, Avg, F, Case, When, IntegerField
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import date, timedelta, datetime
from decimal import Decimal
import json

from Hr.models import (
    Department, Job, Employee,
    AttendanceRecord, LeaveType, EmployeeLeave,
    PayrollPeriod, PayrollEntry, SalaryItem
)


# =============================================================================
# MAIN HR DASHBOARD
# =============================================================================

class HRDashboardView(LoginRequiredMixin, TemplateView):
    """لوحة تحكم الموارد البشرية الشاملة"""
    template_name = 'Hr/dashboard/hr_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('لوحة تحكم الموارد البشرية')
        
        # إحصائيات عامة
        context.update(self.get_general_statistics())
        
        # إحصائيات الموظفين
        context.update(self.get_employee_statistics())
        
        # إحصائيات الحضور
        context.update(self.get_attendance_statistics())
        
        # إحصائيات الإجازات
        context.update(self.get_leave_statistics())
        
        # إحصائيات الرواتب
        context.update(self.get_payroll_statistics())
        
        # البيانات الحديثة
        context.update(self.get_recent_data())
        
        # التنبيهات والإشعارات
        context.update(self.get_alerts_and_notifications())
        
        return context
    
    def get_general_statistics(self):
        """الإحصائيات العامة للنظام"""
        return {
            'total_companies': 1,  # افتراضي
            'active_companies': 1,  # افتراضي
            'total_branches': 1,  # افتراضي
            'total_departments': Department.objects.count(),
            'total_job_positions': Job.objects.count(),
        }
    
    def get_employee_statistics(self):
        """إحصائيات الموظفين"""
        employees = Employee.objects.all()
        
        # إحصائيات الحالة
        status_stats = employees.aggregate(
            total_employees=Count('emp_id'),
            active_employees=Count(Case(When(working_condition='سارى', then=1), output_field=IntegerField())),
            inactive_employees=Count(Case(When(working_condition='استقالة', then=1), output_field=IntegerField())),
            on_leave_employees=Count(Case(When(working_condition='إجازة', then=1), output_field=IntegerField())),
            terminated_employees=Count(Case(When(working_condition='انقطاع عن العمل', then=1), output_field=IntegerField())),
        )
        
        # إحصائيات نوع التوظيف
        employment_stats = employees.values('emp_type').annotate(
            count=Count('emp_id')
        ).order_by('emp_type')
        
        # إحصائيات الأقسام
        department_stats = employees.filter(working_condition='سارى').values(
            'department__dept_name'
        ).annotate(
            count=Count('emp_id')
        ).order_by('-count')[:5]
        
        # الموظفين الجدد (آخر 30 يوم)
        thirty_days_ago = date.today() - timedelta(days=30)
        new_employees = employees.filter(
            emp_date_hiring__gte=thirty_days_ago
        ).count()
        
        return {
            'employee_stats': status_stats,
            'employment_stats': list(employment_stats),
            'department_stats': list(department_stats),
            'new_employees_count': new_employees,
        }
    
    def get_attendance_statistics(self):
        """إحصائيات الحضور"""
        today = date.today()
        current_month = today.replace(day=1)
        
        # إحصائيات اليوم
        today_records = AttendanceRecord.objects.filter(record_date=today)
        
        today_stats = {
            'total_records': today_records.count(),
            'present_employees': today_records.filter(
                record_type='check_in'
            ).values('employee').distinct().count(),
            'late_employees': today_records.filter(record_type='late').count(),
            'overtime_employees': today_records.filter(record_type='overtime').count(),
        }
        
        # حساب الموظفين الغائبين
        present_employee_ids = today_records.filter(
            record_type='check_in'
        ).values_list('employee_id', flat=True)
        
        today_stats['absent_employees'] = Employee.objects.filter(
            working_condition='سارى'
        ).exclude(emp_id__in=present_employee_ids).count()
        
        # إحصائيات الشهر الحالي
        month_records = AttendanceRecord.objects.filter(
            record_date__gte=current_month,
            record_date__lte=today
        )
        
        month_stats = {
            'total_late_minutes': month_records.aggregate(
                total=Sum('late_minutes')
            )['total'] or 0,
            'total_overtime_hours': month_records.aggregate(
                total=Sum('overtime_minutes')
            )['total'] or 0,
            'average_attendance_rate': 0,  # سيتم حسابه لاحقاً
        }
        
        # معدل الحضور الشهري
        working_days = 22  # افتراضي
        total_expected_attendance = Employee.objects.filter(working_condition='سارى').count() * working_days
        actual_attendance = month_records.filter(record_type='check_in').count()
        
        if total_expected_attendance > 0:
            month_stats['average_attendance_rate'] = (actual_attendance / total_expected_attendance) * 100
        
        return {
            'attendance_today': today_stats,
            'attendance_month': month_stats,
        }
    
    def get_leave_statistics(self):
        """إحصائيات الإجازات"""
        today = date.today()
        current_month = today.replace(day=1)
        
        # طلبات الإجازة
        leave_requests = EmployeeLeave.objects.all()

        # إحصائيات الحالة
        status_stats = leave_requests.aggregate(
            total_requests=Count('emp_id'),
            pending_requests=Count(Case(When(status='pending', then=1), output_field=IntegerField())),
            approved_requests=Count(Case(When(status='approved', then=1), output_field=IntegerField())),
            rejected_requests=Count(Case(When(status='rejected', then=1), output_field=IntegerField())),
        )

        # الإجازات الحالية
        current_leaves = leave_requests.filter(
            status='approved',
            start_date__lte=today,
            end_date__gte=today
        ).count()

        # إحصائيات أنواع الإجازات
        leave_type_stats = leave_requests.filter(
            created_at__gte=current_month
        ).values(
            'leave_type__name'
        ).annotate(
            count=Count('emp_id')
        ).order_by('-count')[:5]
        
        return {
            'leave_stats': status_stats,
            'current_leaves': current_leaves,
            'leave_type_stats': list(leave_type_stats),
        }
    
    def get_payroll_statistics(self):
        """إحصائيات الرواتب"""
        current_month = date.today().replace(day=1)
        
        # آخر فترة راتب
        latest_period = PayrollPeriod.objects.order_by('-start_date').first()
        
        payroll_stats = {
            'latest_period': latest_period,
            'total_periods': PayrollPeriod.objects.count(),
            'pending_calculations': PayrollPeriod.objects.filter(status='draft').count(),
        }
        
        if latest_period:
            # إحصائيات آخر فترة
            period_entries = latest_period.payroll_entries.all()
            
            payroll_stats.update({
                'total_employees_paid': period_entries.count(),
                'total_gross_amount': period_entries.aggregate(
                    total=Sum('gross_salary')
                )['total'] or Decimal('0.00'),
                'total_net_amount': period_entries.aggregate(
                    total=Sum('net_salary')
                )['total'] or Decimal('0.00'),
                'average_salary': period_entries.aggregate(
                    avg=Avg('net_salary')
                )['avg'] or Decimal('0.00'),
            })
        
        return {'payroll_stats': payroll_stats}
    
    def get_recent_data(self):
        """البيانات الحديثة"""
        return {
            # الموظفين الجدد
            'recent_employees': Employee.objects.filter(
                working_condition='سارى'
            ).order_by('-emp_date_hiring')[:5],
            
            # طلبات الإجازة الحديثة
            'recent_leave_requests': EmployeeLeave.objects.select_related(
                'employee', 'leave_type'
            ).order_by('-created_at')[:5],
            
            # سجلات الحضور الحديثة
            'recent_attendance': AttendanceRecord.objects.select_related(
                'employee'
            ).filter(
                record_date=date.today()
            ).order_by('-created_at')[:10],
            
            # أعياد الميلاد القادمة
            'upcoming_birthdays': self.get_upcoming_birthdays(),
            
            # العقود المنتهية قريباً
            'expiring_contracts': self.get_expiring_contracts(),
        }
    
    def get_upcoming_birthdays(self):
        """أعياد الميلاد القادمة (خلال 7 أيام)"""
        today = date.today()
        next_week = today + timedelta(days=7)
        
        # تحويل تواريخ الميلاد للسنة الحالية
        employees = Employee.objects.filter(
            working_condition='سارى',
            date_birth__isnull=False
        )
        
        upcoming = []
        for emp in employees:
            birthday_this_year = emp.date_birth.replace(year=today.year)
            
            # إذا كان عيد الميلاد قد مر هذا العام، احسبه للعام القادم
            if birthday_this_year < today:
                birthday_this_year = birthday_this_year.replace(year=today.year + 1)
            
            if today <= birthday_this_year <= next_week:
                upcoming.append({
                    'employee': emp,
                    'birthday': birthday_this_year,
                    'days_until': (birthday_this_year - today).days
                })
        
        return sorted(upcoming, key=lambda x: x['days_until'])[:5]
    
    def get_expiring_contracts(self):
        """العقود المنتهية خلال 30 يوم"""
        today = date.today()
        thirty_days = today + timedelta(days=30)
        
        return Employee.objects.filter(
            working_condition='سارى',
            contract_expiry_date__isnull=False,
            contract_expiry_date__gte=today,
            contract_expiry_date__lte=thirty_days
        ).order_by('contract_expiry_date')[:5]
    
    def get_alerts_and_notifications(self):
        """التنبيهات والإشعارات"""
        alerts = []
        
        # طلبات الإجازة المعلقة
        pending_leaves = EmployeeLeave.objects.filter(status='pending').count()
        if pending_leaves > 0:
            alerts.append({
                'type': 'warning',
                'icon': 'fas fa-calendar-times',
                'title': 'طلبات إجازة معلقة',
                'message': f'يوجد {pending_leaves} طلب إجازة في انتظار الموافقة',
                'url': '/hr/leave/'
            })
        
        # الموظفين الغائبين اليوم
        today_absent = self.get_attendance_statistics()['attendance_today']['absent_employees']
        if today_absent > 0:
            alerts.append({
                'type': 'info',
                'icon': 'fas fa-user-times',
                'title': _('موظفين غائبين'),
                'message': f'يوجد {today_absent} موظف غائب اليوم',
                'url': '/hr/attendance/'
            })
        
        # العقود المنتهية قريباً
        expiring_contracts = len(self.get_expiring_contracts())
        if expiring_contracts > 0:
            alerts.append({
                'type': 'danger',
                'icon': 'fas fa-file-contract',
                'title': _('عقود منتهية قريباً'),
                'message': f'يوجد {expiring_contracts} عقد ينتهي خلال 30 يوم',
                'url': '/hr/employees/'
            })
        
        # فترات رواتب غير محسوبة
        pending_payroll = PayrollPeriod.objects.filter(status='draft').count()
        if pending_payroll > 0:
            alerts.append({
                'type': 'warning',
                'icon': 'fas fa-calculator',
                'title': _('رواتب غير محسوبة'),
                'message': f'يوجد {pending_payroll} فترة راتب لم يتم حسابها',
                'url': '/hr/payroll/'
            })
        
        return {'alerts': alerts}


# =============================================================================
# AJAX DASHBOARD DATA
# =============================================================================

@login_required
def dashboard_data_ajax(request):
    """بيانات لوحة التحكم - AJAX"""
    data_type = request.GET.get('type', 'overview')
    
    if data_type == 'overview':
        return JsonResponse(get_overview_data())
    elif data_type == 'attendance':
        return JsonResponse(get_attendance_chart_data())
    elif data_type == 'leaves':
        return JsonResponse(get_leaves_chart_data())
    elif data_type == 'payroll':
        return JsonResponse(get_payroll_chart_data())
    else:
        return JsonResponse({'error': 'نوع البيانات غير صحيح'})


def get_overview_data():
    """بيانات نظرة عامة"""
    today = date.today()
    
    return {
        'employees': {
            'total': Employee.objects.count(),
            'active': Employee.objects.filter(working_condition='سارى').count(),
            'new_this_month': Employee.objects.filter(
                emp_date_hiring__gte=today.replace(day=1)
            ).count(),
        },
        'attendance': {
            'present_today': AttendanceRecord.objects.filter(
                record_date=today,
                record_type='check_in'
            ).values('employee').distinct().count(),
            'late_today': AttendanceRecord.objects.filter(
                record_date=today,
                record_type='late'
            ).count(),
        },
        'leaves': {
            'pending': EmployeeLeave.objects.filter(status='pending').count(),
            'approved_today': EmployeeLeave.objects.filter(
                status='approved',
                start_date__lte=today,
                end_date__gte=today
            ).count(),
        }
    }


def get_attendance_chart_data():
    """بيانات مخطط الحضور"""
    # آخر 7 أيام
    dates = []
    present_data = []
    late_data = []
    absent_data = []
    
    for i in range(6, -1, -1):
        date_obj = date.today() - timedelta(days=i)
        dates.append(date_obj.strftime('%Y-%m-%d'))
        
        day_records = AttendanceRecord.objects.filter(record_date=date_obj)

        present_count = day_records.filter(
            record_type='check_in'
        ).values('employee').distinct().count()

        late_count = day_records.filter(record_type='late').count()
        
        # حساب الغائبين
        total_active = Employee.objects.filter(working_condition='سارى').count()
        absent_count = total_active - present_count
        
        present_data.append(present_count)
        late_data.append(late_count)
        absent_data.append(absent_count)
    
    return {
        'labels': dates,
        'datasets': [
            {
                'label': 'حاضر',
                'data': present_data,
                'backgroundColor': 'rgba(40, 167, 69, 0.8)',
            },
            {
                'label': 'متأخر',
                'data': late_data,
                'backgroundColor': 'rgba(255, 193, 7, 0.8)',
            },
            {
                'label': 'غائب',
                'data': absent_data,
                'backgroundColor': 'rgba(220, 53, 69, 0.8)',
            }
        ]
    }


def get_leaves_chart_data():
    """بيانات مخطط الإجازات"""
    # إحصائيات أنواع الإجازات
    leave_types = LeaveType.objects.filter(is_active=True)
    
    labels = []
    data = []
    colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ]
    
    for i, leave_type in enumerate(leave_types):
        count = EmployeeLeave.objects.filter(
            leave_type=leave_type,
            status='approved',
            start_date__year=date.today().year
        ).count()

        if count > 0:
            labels.append(leave_type.name)
            data.append(count)
    
    return {
        'labels': labels,
        'datasets': [{
            'data': data,
            'backgroundColor': colors[:len(data)],
        }]
    }


def get_payroll_chart_data():
    """بيانات مخطط الرواتب"""
    # آخر 6 أشهر
    months = []
    amounts = []
    
    for i in range(5, -1, -1):
        month_date = date.today().replace(day=1) - timedelta(days=i*30)
        months.append(month_date.strftime('%Y-%m'))
        
        # البحث عن فترة الراتب لهذا الشهر
        period = PayrollPeriod.objects.filter(
            start_date__year=month_date.year,
            start_date__month=month_date.month
        ).first()
        
        if period:
            total_amount = period.payroll_entries.aggregate(
                total=Sum('net_salary')
            )['total'] or 0
            amounts.append(float(total_amount))
        else:
            amounts.append(0)
    
    return {
        'labels': months,
        'datasets': [{
            'label': 'إجمالي الرواتب',
            'data': amounts,
            'borderColor': 'rgba(54, 162, 235, 1)',
            'backgroundColor': 'rgba(54, 162, 235, 0.2)',
            'fill': True,
        }]
    }


# =============================================================================
# QUICK ACTIONS
# =============================================================================

@login_required
def quick_employee_search(request):
    """البحث السريع عن الموظفين"""
    query = request.GET.get('q', '')
    
    if len(query) < 2:
        return JsonResponse({'employees': []})
    
    employees = Employee.objects.filter(
        Q(emp_id__icontains=query) |
        Q(emp_first_name__icontains=query) |
        Q(emp_second_name__icontains=query) |
        Q(emp_full_name__icontains=query)
    ).filter(working_condition='سارى')[:10]
    
    results = [
        {
            'id': emp.emp_id,
            'employee_number': emp.emp_id,
            'full_name': emp.emp_full_name,
            'department': emp.department.dept_name if emp.department else '',
            'job_position': emp.jop_name if emp.jop_name else '',
            'photo_url': emp.emp_image.url if emp.emp_image else None,
            'url': f'/hr/employees/{emp.emp_id}/'
        }
        for emp in employees
    ]
    
    return JsonResponse({'employees': results})


@login_required
def dashboard_notifications(request):
    """إشعارات لوحة التحكم"""
    notifications = []
    
    # طلبات الإجازة الجديدة
    new_leave_requests = EmployeeLeave.objects.filter(
        status='pending',
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).count()

    if new_leave_requests > 0:
        notifications.append({
            'type': 'leave_request',
            'count': new_leave_requests,
            'message': f'طلب إجازة جديد' if new_leave_requests == 1 else f'{new_leave_requests} طلبات إجازة جديدة',
            'url': '/hr/leave/'
        })
    
    # موظفين جدد
    new_employees = Employee.objects.filter(
        emp_date_hiring__gte=timezone.now() - timedelta(hours=24)
    ).count()
    
    if new_employees > 0:
        notifications.append({
            'type': 'new_employee',
            'count': new_employees,
            'message': f'موظف جديد' if new_employees == 1 else f'{new_employees} موظفين جدد',
            'url': '/hr/employees/'
        })
    
    return JsonResponse({'notifications': notifications})
